<template>
  <div class="product-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>商品管理</span>
        </div>
      </template>

      <!-- 商品表格 -->
      <el-table :data="productList" style="width: 100%" v-loading="loading">
        <el-table-column prop="productId" label="商品ID" width="100" />
        <el-table-column prop="productName" label="商品名称" width="200" />
        <el-table-column prop="price" label="价格" width="120">
          <template #default="scope">
            <span>¥{{ scope.row.price?.toFixed(2) || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="storageNum" label="库存数量" width="120" />
        <el-table-column prop="description" label="商品描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="productImages" label="商品图片" width="150">
          <template #default="scope">
            <el-image
              v-if="scope.row.productImages"
              :src="scope.row.productImages"
              :preview-src-list="[scope.row.productImages]"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-teleported="true"
            />
            <span v-else class="no-image">暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="downloadImage(scope.row.productId)"
              :disabled="!scope.row.productImages"
            >
              下载图片
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { get } from '../../api/api.js'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const productList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 获取商品列表
const getProductList = async () => {
  loading.value = true
  try {
    const response = await get('/getAllProducts')
    console.log('商品列表响应:', response)
    
    if (Array.isArray(response)) {
      // 如果直接返回数组
      productList.value = response
      total.value = response.length
    } else if (response.code === 200) {
      // 如果返回标准格式
      productList.value = response.data || []
      total.value = response.total ?? response.data?.length ?? 0
    } else {
      ElMessage.error(response.message || '获取商品列表失败')
      console.error('获取商品列表失败:', response)
    }
  } catch (error) {
    ElMessage.error('获取商品列表失败: ' + error.message)
    console.error('获取商品列表异常:', error)
  } finally {
    loading.value = false
  }
}

// 下载商品图片
const downloadImage = async (productId) => {
  try {
    loading.value = true
    
    // 创建下载链接
    const downloadUrl = `http://localhost:8082/download?productId=${productId}`
    
    // 获取token用于认证
    const token = localStorage.getItem('token')
    
    // 使用fetch下载文件
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'token': token || ''
      }
    })
    
    if (!response.ok) {
      throw new Error('下载失败')
    }
    
    // 获取文件名（从响应头或使用默认名称）
    const contentDisposition = response.headers.get('Content-Disposition')
    let filename = `product_${productId}_image.jpg`
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }
    
    // 创建blob并下载
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('图片下载成功')
  } catch (error) {
    ElMessage.error('图片下载失败: ' + error.message)
    console.error('下载图片异常:', error)
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  // 如果需要服务端分页，可以重新调用API
  // getProductList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  // 如果需要服务端分页，可以重新调用API
  // getProductList()
}

// 页面加载时获取数据
onMounted(() => {
  getProductList()
})
</script>

<style scoped>
.product-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.no-image {
  color: #999;
  font-size: 12px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 按钮样式 */
.el-button {
  border-radius: 4px;
}

.el-button--small {
  padding: 5px 15px;
}

/* 图片预览样式 */
:deep(.el-image) {
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s;
}

:deep(.el-image:hover) {
  border-color: #409eff;
  transform: scale(1.05);
}
</style>
