<template>
  <div class="registration-container">
    <el-card class="registration-card">
      <template #header>
        <div class="card-title">
          <h2>创建新账户</h2>
          <p>填写以下信息完成注册</p>
        </div>
      </template>

      <el-form
        :model="registrationData"
        :rules="formValidationRules"
        ref="registrationFormRef"
        label-width="auto"
        class="registration-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registrationData.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registrationData.password"
            type="password"
            placeholder="请输入密码"
            show-password
            prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registrationData.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
            prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registrationData.email"
            type="email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item class="action-buttons">
          <el-button
            type="primary"
            @click="handleRegistration"
            :loading="isRegistering"
            size="large"
          >
            {{ isRegistering ? '注册中...' : '确认注册' }}
          </el-button>
          <el-button
            @click="navigateToLogin"
            size="large"
          >
            返回登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { submitData } from '../api/api.js'

const router = useRouter()

// 表单数据
const registrationData = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  email: ''
})

const registrationFormRef = ref(null)
const isRegistering = ref(false)

// 表单验证规则
const formValidationRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registrationData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 处理注册
const handleRegistration = async () => {
  try {
    const valid = await registrationFormRef.value.validate()
    if (!valid) return

    isRegistering.value = true

    // 准备注册数据
    const registerData = {
      username: registrationData.username,
      password: registrationData.password,
      email: registrationData.email
    }

    console.log('发送注册请求:', registerData)

    // 调用后端注册API
    const response = await submitData('/admin/register', registerData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })

    console.log('注册响应:', response)

    if (response.code === 200) {
      ElMessage.success(response.message || '注册成功！')
      // 清空表单
      Object.keys(registrationData).forEach(key => {
        registrationData[key] = ''
      })
      // 跳转到登录页面
      router.push('/login')
    } else {
      ElMessage.error(response.message || '注册失败，请重试')
    }
  } catch (error) {
    console.error('注册错误:', error)
    ElMessage.error('注册失败：' + (error.message || '网络连接异常'))
  } finally {
    isRegistering.value = false
  }
}

// 返回登录页面
const navigateToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
/* 简洁的注册页面样式 */
.registration-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.registration-card {
  width: 100%;
  max-width: 450px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-title {
  text-align: center;
  color: #333;
}

.card-title h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.card-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.registration-form {
  padding: 20px 0;
}

.action-buttons {
  text-align: center;
  margin-top: 20px;
}

.action-buttons .el-button {
  margin: 0 10px;
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .registration-container {
    padding: 10px;
  }

  .registration-card {
    margin: 0 10px;
  }

  .action-buttons .el-button {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}
</style>