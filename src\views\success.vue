<template>
  <div class="payment-success">
    <el-card class="success-card">
      <!-- 成功图标 -->
      <div class="icon-container">
       <el-result icon="success" title="支付成功">
         <template #sub-title>
           <div style="white-space: pre-line;">
             订单号：{{ orderId }} <br />
             支付金额：<span class="price">￥{{ price }}</span><br />
             支付时间：{{ time }}
           </div>
         </template>
       </el-result>

      </div>

      <!-- 操作按钮 -->
      <div class="button-group">
        <el-button type="primary" @click="goHome">返回首页</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {get} from '../api/api.js'

const router = useRouter()

const orderId = ref('')
const price = ref('')
const time = ref('')

// 获取 URL 参数函数
const getQueryString = (name) => {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  const r = window.location.search.substr(1).match(reg)
  if (r != null) {
    return decodeURI(r[2])
  }
  return null
}

// 生成订单（调用后端）
const createOrder = async () => {
  try {
    const res = await get('/createOrder', {
        productId:localStorage.getItem('productId'),
        totalPrice: getQueryString('total_amount'),
        price: getQueryString('total_amount'),
      }
    )
    console.log(res.data)
  } catch (err) {
    console.error('创建订单失败', err)
  }
}

// 返回首页
const goHome = () => {
  router.push('/userIndex')
}

onMounted(() => {
  orderId.value = getQueryString('out_trade_no')
  console.log('orderId.value',orderId.value);
  price.value = getQueryString('total_amount')
  time.value = decodeURIComponent(getQueryString('timestamp'))
  createOrder();
})
</script>

<style scoped>
.payment-success {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f5f5f5;
}

.success-card {
  width: 400px;
  padding: 20px;
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.icon-container {
  margin-bottom: 20px;
}

.price {
  color: #ff5722;
  font-weight: bold;
}

.button-group {
  margin-top: 20px;
}
</style>
