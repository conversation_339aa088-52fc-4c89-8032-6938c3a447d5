<template>
  <div class="simple-sidebar">
    <el-menu
      :default-active="currentRouter"
      class="sidebar-menu"
      @select="handleSelect"
      router
    >
      <el-menu-item index="/dashboard">
        <el-icon><HomeFilled /></el-icon>
        <span>系统概览</span>
      </el-menu-item>

      <el-menu-item index="/employee/list">
        <el-icon><UserFilled /></el-icon>
        <span>员工管理</span>
      </el-menu-item>

      <el-menu-item index="/department/list">
        <el-icon><OfficeBuilding /></el-icon>
        <span>部门管理</span>
      </el-menu-item>

      <el-menu-item index="/rose-chart-demo">
        <el-icon><PieChart /></el-icon>
        <span>玫瑰图演示</span>
      </el-menu-item>

      <el-menu-item index="/upload">
        <el-icon><PieChart /></el-icon>
        <span>文件上传</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
/**
 * Modern Sidebar Navigation Component
 * <AUTHOR>
 * @description Glassmorphism design with grouped navigation items
 */

import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const currentRouter = ref("")

// Handle menu item selection
const handleSelect = (index: string) => {
  router.push(index)
}

// Set current active route
onMounted(() => {
  currentRouter.value = route.path
})
</script>

<style scoped>
.simple-sidebar {
  height: 100%;
  background: #fff;
}

.sidebar-menu {
  border-right: none;
  background: #fff;
}

:deep(.el-menu-item) {
  color: #333;
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-menu-item:hover) {
  background-color: #f5f5f5;
  color: #409eff;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff;
  color: #fff;
}

:deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}
</style>
