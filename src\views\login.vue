<template>
  <div class="authentication-container">
    <Particles
      id="background-particles"
      class="particle-background"
      :options="particleConfiguration"
    />

    <div class="auth-form-wrapper">
      <h2 class="auth-title">系统登录</h2>
      <el-form
        :model="credentials"
        ref="authFormRef"
        :rules="validationRules"
        label-width="100px"
        class="login-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="credentials.username"
            placeholder="请输入用户名"
            clearable
            prefix-icon="User"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            type="password"
            v-model="credentials.password"
            placeholder="请输入密码"
            show-password
            clearable
            prefix-icon="Lock"
            @keyup.enter="handleAuthentication"
          />
        </el-form-item>

        <el-button
          class="auth-submit-btn"
          type="primary"
          @click="handleAuthentication"
          :loading="isAuthenticating"
          auto-insert-space
        >
          {{ isAuthenticating ? '登录中...' : '登录' }}
        </el-button>

        <div class="auth-links">
          <el-link
            type="primary"
            @click="navigateToPage('/register')"
            class="register-link"
          >
            没有账号？去注册
          </el-link>
          <el-link
            type="danger"
            @click="navigateToPage('/forgot-password')"
            class="forgot-link"
          >
            忘记密码？
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
/**
 * Authentication Login Component
 * <AUTHOR>
 * @description Handles user authentication with enhanced UX
 */

import { reactive, ref } from "vue"
import { fetchData, submitData } from '../api/api.js'
import { useRouter } from "vue-router"
import { ElNotification } from 'element-plus'

// Reactive state management
const credentials = reactive({
  username: "",
  password: "",
})

const authFormRef = ref(null)
const isAuthenticating = ref(false)
const router = useRouter()

// Form validation configuration
const validationRules = {
  username: [
    {
      required: true,
      message: "用户名为必填项",
      trigger: "blur"
    },
    {
      min: 2,
      max: 50,
      message: "用户名长度应在2-50字符之间",
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: "密码为必填项",
      trigger: "blur",
    },
    {
      min: 6,
      message: "密码长度不能少于6位",
      trigger: "blur"
    }
  ],
}

/**
 * Handle user authentication process
 */
const handleAuthentication = async () => {
  if (!authFormRef.value) return

  try {
    // Validate form before submission
    await authFormRef.value.validate()

    isAuthenticating.value = true

    // 使用POST请求发送表单数据
    const formData = new URLSearchParams()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const authResponse = await submitData("/admin/login", formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    console.log('Authentication response:', authResponse)

    if (authResponse.code === 200) {
      // Success notification
      ElNotification({
        title: '登录成功',
        message: authResponse.message || '欢迎回来！',
        type: 'success',
        duration: 3000
      })

      // 处理JWT token和用户数据
      if (authResponse.data && authResponse.data.token) {
        // 保存JWT token
        localStorage.setItem('token', authResponse.data.token)
        // 保存用户信息
        localStorage.setItem('admin', JSON.stringify(authResponse.data.admin))
        // 设置用户状态
        localStorage.setItem('userStatus', credentials.username)

        console.log('JWT Token saved:', authResponse.data.token)
      } else {
        // 兼容旧版本响应格式
        localStorage.setItem('admin', JSON.stringify(authResponse.data))
        localStorage.setItem('userStatus', credentials.username)
      }

      // Navigate to dashboard
      await router.push("/")

    } else {
      // Error notification
      ElNotification({
        title: '登录失败',
        message: authResponse.message || '用户名或密码错误',
        type: 'error',
        duration: 4000
      })
    }

  } catch (validationError) {
    console.error('Authentication failed:', validationError)
    ElNotification({
      title: '登录异常',
      message: '网络连接异常，请稍后重试',
      type: 'error',
      duration: 4000
    })
  } finally {
    isAuthenticating.value = false
  }
}

/**
 * Navigate to different authentication pages
 * @param {string} targetPath - Target route path
 */
const navigateToPage = (targetPath) => {
  router.replace(targetPath)
}
/**
 * Particle animation configuration for enhanced visual appeal
 */
const particleConfiguration = {
  fpsLimit: 60,
  interactivity: {
    detectsOn: "canvas",
    events: {
      onClick: {
        enable: true,
        mode: "push",
      },
      onHover: {
        enable: true,
        mode: "grab",
      },
      resize: true,
    },
    modes: {
      bubble: {
        distance: 400,
        duration: 2,
        opacity: 0.8,
        size: 40,
      },
      push: {
        quantity: 4,
      },
      grab: {
        distance: 200,
        duration: 0.4,
      },
      attract: {
        distance: 200,
        duration: 0.4,
        factor: 5,
      },
    },
  },
  particles: {
    color: {
      value: "#BA55D3",
    },
    links: {
      color: "#FFBBFF",
      distance: 150,
      enable: true,
      opacity: 0.4,
      width: 1.2,
    },
    collisions: {
      enable: true,
    },
    move: {
      attract: { enable: false, rotateX: 600, rotateY: 1200 },
      bounce: false,
      direction: "none",
      enable: true,
      out_mode: "out",
      random: false,
      speed: 0.5,
      straight: false,
    },
    number: {
      density: {
        enable: true,
        value_area: 800,
      },
      value: 80,
    },
    opacity: {
      value: 0.7,
    },
    shape: {
      type: "star",
    },
    size: {
      random: true,
      value: 3,
    },
  },
  detectRetina: true,
}
</script>
 

 
<style lang="scss" scoped>
/**
 * Authentication page styling with modern design principles
 */

.authentication-container {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.particle-background {
  height: 100%;
  width: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url("@/assets/images/login-bg.png");
  opacity: 0.9;
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.auth-form-wrapper {
  position: relative;
  z-index: 10;
  width: 480px;
  padding: 60px 50px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-sizing: border-box;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

.auth-title {
  margin: 0 0 40px;
  padding: 0;
  color: #fff;
  text-align: center;
  font-size: 28px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.login-form {
  transform: translateX(-30px);
}

.auth-submit-btn {
  width: 120px;
  height: 45px;
  font-size: 16px;
  font-weight: 500;
  margin: 20px 0;
  transform: translateX(170px);
  border-radius: 25px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(170px) scale(1.05);
  }
}

.auth-links {
  text-align: right;
  margin-top: 30px;

  .register-link {
    margin-right: 15px;
    font-weight: 500;
  }

  .forgot-link {
    font-weight: 500;
  }
}

// Enhanced form styling
:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  backdrop-filter: blur(5px);

  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
  }

  &.is-focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

:deep(.el-input__inner) {
  color: #fff;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
