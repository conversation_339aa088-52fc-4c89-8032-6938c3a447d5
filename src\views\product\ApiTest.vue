<template>
  <div class="api-test">
    <el-card>
      <template #header>
        <span>API连接测试</span>
      </template>
      
      <div class="test-section">
        <h3>后端连接测试</h3>
        <el-button @click="testConnection" type="primary" :loading="testing">
          测试后端连接
        </el-button>
        <div v-if="connectionResult" class="result-box">
          <h4>连接结果：</h4>
          <pre>{{ connectionResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>商品API测试</h3>
        <el-button @click="testProductApi" type="success" :loading="testingProduct">
          测试商品API
        </el-button>
        <div v-if="productResult" class="result-box">
          <h4>商品API结果：</h4>
          <pre>{{ productResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>直接访问测试</h3>
        <p>请在浏览器中直接访问以下URL来测试后端：</p>
        <ul>
          <li><a href="http://localhost:8082/product/getAllProducts" target="_blank">http://localhost:8082/product/getAllProducts</a></li>
          <li><a href="http://localhost:8082/product/download?productId=1" target="_blank">http://localhost:8082/product/download?productId=1</a></li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { get } from '../../api/api.js'
import { ElMessage } from 'element-plus'

const testing = ref(false)
const testingProduct = ref(false)
const connectionResult = ref('')
const productResult = ref('')

// 测试基本连接
const testConnection = async () => {
  testing.value = true
  connectionResult.value = ''
  
  try {
    // 尝试直接使用fetch测试连接
    const response = await fetch('http://localhost:8082/product/getAllProducts', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    const data = await response.text()
    
    connectionResult.value = JSON.stringify({
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data: data
    }, null, 2)
    
    if (response.ok) {
      ElMessage.success('后端连接成功！')
    } else {
      ElMessage.error(`后端返回错误状态: ${response.status}`)
    }
    
  } catch (error) {
    connectionResult.value = JSON.stringify({
      error: error.message,
      type: error.constructor.name,
      stack: error.stack
    }, null, 2)
    
    ElMessage.error('连接失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 测试商品API
const testProductApi = async () => {
  testingProduct.value = true
  productResult.value = ''
  
  try {
    console.log('开始测试商品API...')
    const response = await get('/product/getAllProducts')
    
    productResult.value = JSON.stringify({
      success: true,
      data: response
    }, null, 2)
    
    ElMessage.success('商品API调用成功！')
    
  } catch (error) {
    productResult.value = JSON.stringify({
      error: error.message,
      response: error.response?.data,
      status: error.response?.status,
      statusText: error.response?.statusText
    }, null, 2)
    
    ElMessage.error('商品API调用失败: ' + error.message)
  } finally {
    testingProduct.value = false
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.result-box {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.result-box pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.test-section ul {
  margin: 10px 0;
}

.test-section li {
  margin: 5px 0;
}

.test-section a {
  color: #409eff;
  text-decoration: none;
}

.test-section a:hover {
  text-decoration: underline;
}
</style>
