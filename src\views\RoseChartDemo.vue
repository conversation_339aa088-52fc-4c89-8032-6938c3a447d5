<template>
  <div class="rose-chart-demo">
    <div class="demo-header">
      <h1>ECharts 玫瑰图组件演示</h1>
      <p>基于提供的数组数据创建的玫瑰图组件，支持多种配置选项</p>
    </div>

    <!-- 控制面板 -->
    <el-card class="control-panel" shadow="hover">
      <template #header>
        <span>配置选项</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="图表标题">
            <el-input v-model="chartTitle" placeholder="请输入图表标题" />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="玫瑰图类型">
            <el-select v-model="roseType" placeholder="选择类型">
              <el-option label="半径模式" value="radius" />
              <el-option label="面积模式" value="area" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="图例位置">
            <el-select v-model="legendPosition" placeholder="选择位置">
              <el-option label="右侧" value="right" />
              <el-option label="左侧" value="left" />
              <el-option label="顶部" value="top" />
              <el-option label="底部" value="bottom" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="显示图例">
            <el-switch v-model="showLegend" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表展示区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 默认数据图表 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <span>默认数据玫瑰图</span>
          </template>
          <div ref="defaultChartRef" style="width: 100%; height: 350px;"></div>
        </el-card>
      </el-col>

      <!-- 自定义数据图表 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <span>自定义数据玫瑰图</span>
          </template>
          <div ref="customChartRef" style="width: 100%; height: 350px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据展示 -->
    <el-card style="margin-top: 20px;" shadow="hover">
      <template #header>
        <span>原始数据</span>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="默认数据" name="default">
          <el-table :data="defaultData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="label" label="标签" />
            <el-table-column prop="count" label="数量" />
            <el-table-column prop="color" label="颜色">
              <template #default="scope">
                <div class="color-preview">
                  <span 
                    class="color-block" 
                    :style="{ backgroundColor: scope.row.color }"
                  ></span>
                  {{ scope.row.color }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="自定义数据" name="custom">
          <el-table :data="customData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="label" label="标签" />
            <el-table-column prop="count" label="数量" />
            <el-table-column prop="color" label="颜色">
              <template #default="scope">
                <div class="color-preview">
                  <span 
                    class="color-block" 
                    :style="{ backgroundColor: scope.row.color }"
                  ></span>
                  {{ scope.row.color }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>


  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// 响应式数据
const chartTitle = ref('数据分布玫瑰图')
const roseType = ref('radius')
const legendPosition = ref('right')
const showLegend = ref(true)
const activeTab = ref('default')

// 图表引用
const defaultChartRef = ref(null)
const customChartRef = ref(null)
let defaultChart = null
let customChart = null

// 题目提供的默认数据
const defaultData = ref([
  { id: 1, label: 'rose 1', count: 40, color: '#FF6B6B' },
  { id: 2, label: 'rose 2', count: 38, color: '#FFD93D' },
  { id: 3, label: 'rose 3', count: 32, color: '#6BCB77' },
  { id: 4, label: 'rose 4', count: 30, color: '#4D96FF' },
  { id: 5, label: 'rose 5', count: 28, color: '#B983FF' },
  { id: 6, label: 'rose 6', count: 26, color: '#FF9F1C' },
  { id: 7, label: 'rose 7', count: 22, color: '#8338EC' },
  { id: 8, label: 'rose 8', count: 18, color: '#3A86FF' }
])

// 自定义数据示例
const customData = ref([
  { id: 1, label: '产品A', count: 120, color: '#FF6B6B' },
  { id: 2, label: '产品B', count: 98, color: '#4ECDC4' },
  { id: 3, label: '产品C', count: 86, color: '#45B7D1' },
  { id: 4, label: '产品D', count: 72, color: '#96CEB4' },
  { id: 5, label: '产品E', count: 65, color: '#FFEAA7' },
  { id: 6, label: '产品F', count: 54, color: '#DDA0DD' }
])

// 初始化图表
const initCharts = async () => {
  await nextTick()

  if (defaultChartRef.value) {
    defaultChart = echarts.init(defaultChartRef.value)
  }

  if (customChartRef.value) {
    customChart = echarts.init(customChartRef.value)
  }

  updateCharts()
}

// 更新图表
const updateCharts = () => {
  updateDefaultChart()
  updateCustomChart()
}

// 更新默认图表
const updateDefaultChart = () => {
  if (!defaultChart) return

  const chartData = defaultData.value.map(item => ({
    name: item.label,
    value: item.count,
    itemStyle: { color: item.color }
  }))

  const option = createChartOption(chartData, chartTitle.value, roseType.value, legendPosition.value, showLegend.value)
  defaultChart.setOption(option, true)
}

// 更新自定义图表
const updateCustomChart = () => {
  if (!customChart) return

  const chartData = customData.value.map(item => ({
    name: item.label,
    value: item.count,
    itemStyle: { color: item.color }
  }))

  const option = createChartOption(chartData, '销售数据分析', 'area', 'bottom', true)
  customChart.setOption(option, true)
}

// 创建图表配置
const createChartOption = (data, title, roseType, legendPos, showLeg) => {
  const legendConfig = showLeg ? {
    show: true,
    orient: ['top', 'bottom'].includes(legendPos) ? 'horizontal' : 'vertical',
    [legendPos]: 10,
    textStyle: { fontSize: 12, color: '#666' }
  } : { show: false }

  return {
    title: {
      text: title,
      left: 'center',
      top: 20,
      textStyle: { fontSize: 16, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: legendConfig,
    series: [{
      name: title,
      type: 'pie',
      radius: ['20%', '70%'],
      center: ['50%', '60%'],
      roseType: roseType,
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
}

// 监听配置变化
watch([chartTitle, roseType, legendPosition, showLegend], () => {
  updateDefaultChart()
})

// 窗口大小变化处理
const handleResize = () => {
  if (defaultChart) defaultChart.resize()
  if (customChart) customChart.resize()
}

// 生命周期
onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (defaultChart) {
    defaultChart.dispose()
    defaultChart = null
  }
  if (customChart) {
    customChart.dispose()
    customChart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.rose-chart-demo {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 16px;
}

.control-panel {
  margin-bottom: 20px;
}

.color-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
}


</style>
