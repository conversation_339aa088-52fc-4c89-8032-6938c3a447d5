<template>
<div class="container">

<el-card style="max-width: 480px" v-for="product in products">
     <template #header>{{product.productName}}</template>
    <img
      src="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png"
      style="width: 100%"
    />
    <template #footer>￥{{product.price}}</template>
    <el-button type="warning" @click="buy(product.productId)">立即购买</el-button>
  </el-card>

</div>



</template>

<script setup>
import { get } from "../../api/api";
import { ref,onMounted } from "vue";

const products=ref([]);

onMounted(async()=>{
  const data=await get("/getProductList");
products.value=data.data;
});


const buy=(productId)=>{//立即购买

    localStorage.setItem("productId",productId);
//不能使用axios发送支付请求
    window.location.href="http://localhost:8082/pay?productId="+productId;

}


</script>

<style scoped>
.container{
    display: flex;
}


.container .el-card{
    margin: 10px;
}
</style>