<template>
  <div class="rose-chart-container">
    <div class="chart-header" v-if="title">
      <h3>{{ title }}</h3>
    </div>
    <div ref="chartRef" :style="{ width: width, height: height }" class="rose-chart"></div>
  </div>
</template>

<script setup>
/**
 * ECharts Rose Chart Component
 * @description 基于ECharts的玫瑰图组件，支持自定义数据和样式
 */

import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props定义
const props = defineProps({
  // 图表数据
  data: {
    type: Array,
    default: () => [
      { id: 1, label: 'rose 1', count: 40, color: '#FF6B6B' },
      { id: 2, label: 'rose 2', count: 38, color: '#FFD93D' },
      { id: 3, label: 'rose 3', count: 32, color: '#6BCB77' },
      { id: 4, label: 'rose 4', count: 30, color: '#4D96FF' },
      { id: 5, label: 'rose 5', count: 28, color: '#B983FF' },
      { id: 6, label: 'rose 6', count: 26, color: '#FF9F1C' },
      { id: 7, label: 'rose 7', count: 22, color: '#8338EC' },
      { id: 8, label: 'rose 8', count: 18, color: '#3A86FF' }
    ]
  },
  // 图表标题
  title: {
    type: String,
    default: '玫瑰图'
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // 图表高度
  height: {
    type: String,
    default: '400px'
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true
  },
  // 图例位置
  legendPosition: {
    type: String,
    default: 'right', // 'left', 'right', 'top', 'bottom'
    validator: (value) => ['left', 'right', 'top', 'bottom'].includes(value)
  },
  // 玫瑰图类型
  roseType: {
    type: String,
    default: 'radius', // 'radius' 或 'area'
    validator: (value) => ['radius', 'area'].includes(value)
  }
})

// 响应式数据
const chartRef = ref(null)
let chartInstance = null

// 初始化图表
const initChart = async () => {
  await nextTick()
  
  if (!chartRef.value) return
  
  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  // 创建新的图表实例
  chartInstance = echarts.init(chartRef.value)
  
  // 设置图表配置
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.data || props.data.length === 0) return
  
  // 处理数据，提取label和count
  const chartData = props.data.map(item => ({
    name: item.label,
    value: item.count,
    itemStyle: {
      color: item.color || '#5470c6'
    }
  }))
  
  // 配置图例位置
  const legendConfig = props.showLegend ? {
    show: true,
    orient: ['top', 'bottom'].includes(props.legendPosition) ? 'horizontal' : 'vertical',
    [props.legendPosition]: 10,
    textStyle: {
      fontSize: 12,
      color: '#666'
    }
  } : { show: false }
  
  // ECharts配置选项
  const option = {
    title: {
      text: props.title,
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: legendConfig,
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['50%', '60%'],
        roseType: props.roseType,
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200
        }
      }
    ]
  }
  
  // 设置图表选项
  chartInstance.setOption(option, true)
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听其他props变化
watch([() => props.title, () => props.showLegend, () => props.legendPosition, () => props.roseType], () => {
  updateChart()
})

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 生命周期钩子
onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法给父组件
defineExpose({
  // 获取图表实例
  getChartInstance: () => chartInstance,
  // 重新渲染图表
  refresh: updateChart,
  // 调整图表大小
  resize: handleResize
})
</script>

<style scoped>
.rose-chart-container {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  padding: 16px 20px 0;
  text-align: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.rose-chart {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header h3 {
    font-size: 16px;
  }
}
</style>
